# ERP进销存管理系统

一个面向小型企业的轻量级ERP进销存管理系统，基于Vue 3 + TypeScript + Node.js + SQLite构建。

## 项目概述

### 技术栈

**前端**
- Vue 3 + TypeScript
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router 路由管理
- Vite 构建工具
- Axios HTTP客户端

**后端**
- Node.js + Express
- TypeScript
- SQLite 数据库
- JWT 身份认证
- bcryptjs 密码加密

## 项目结构

```
jjs生产管理软件/
├── frontend/          # Vue 3前端项目
│   ├── src/
│   │   ├── api/          # API接口
│   │   ├── components/   # 通用组件
│   │   ├── views/        # 页面组件
│   │   ├── stores/       # Pinia状态管理
│   │   ├── router/       # 路由配置
│   │   └── main.ts       # 入口文件
│   ├── package.json
│   └── vite.config.ts
├── backend/           # Node.js后端项目
│   ├── src/
│   │   ├── controllers/  # 控制器
│   │   ├── models/       # 数据模型
│   │   ├── routes/       # 路由定义
│   │   ├── middleware/   # 中间件
│   │   ├── types/        # TypeScript类型定义
│   │   └── index.ts      # 入口文件
│   ├── data/             # SQLite数据库文件
│   ├── package.json
│   └── tsconfig.json
├── docs/              # 项目文档
└── README.md
```

## 已实现功能

### ✅ 第一阶段功能（已完成）

1. **项目搭建**
   - ✅ Vue 3 + TypeScript前端项目
   - ✅ Node.js + Express后端项目
   - ✅ SQLite数据库配置
   - ✅ 开发环境配置

2. **用户认证系统**
   - ✅ 用户注册/登录
   - ✅ JWT Token认证
   - ✅ 路由守卫
   - ✅ 登录页面UI

3. **基础数据管理**
   - ✅ 原材料管理（CRUD）
   - ✅ 原材料列表页面
   - ✅ 原材料新增/编辑功能
   - ✅ 数据验证和搜索
   - 🚧 成品管理（占位符页面）

4. **系统基础功能**
   - ✅ 首页仪表板
   - ✅ 导航菜单
   - ✅ 用户信息显示
   - ✅ 通用组件（DataTable、FormDialog、LoadingState、EmptyState）

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd jjs生产管理软件
```

2. **安装后端依赖**
```bash
cd backend
npm install
```

3. **安装前端依赖**
```bash
cd ../frontend
npm install
```

4. **启动后端服务器**
```bash
cd ../backend
npm run dev
```
后端服务器将运行在 http://localhost:3000

5. **启动前端开发服务器**
```bash
cd ../frontend
npm run dev
```
前端应用将运行在 http://localhost:5173 或 http://localhost:5174

### 默认账户

- 用户名: admin
- 密码: 123456
- 角色: 管理员

## API接口

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

### 原材料接口
- `GET /api/materials` - 获取原材料列表
- `GET /api/materials/:id` - 获取单个原材料
- `POST /api/materials` - 创建原材料
- `PUT /api/materials/:id` - 更新原材料
- `DELETE /api/materials/:id` - 删除原材料

## 数据库结构

### 用户表 (users)
- id: 主键
- username: 用户名
- password: 密码（加密）
- email: 邮箱
- role: 角色（admin/user）
- created_at: 创建时间
- updated_at: 更新时间

### 原材料表 (materials)
- id: 主键
- code: 编码
- name: 名称
- specification: 规格
- unit: 单位
- cost_price: 成本价
- stock_min: 最小库存
- stock_max: 最大库存
- current_stock: 当前库存
- status: 状态（active/inactive）
- created_at: 创建时间
- updated_at: 更新时间

### 成品表 (products)
- id: 主键
- code: 编码
- name: 名称
- specification: 规格
- unit: 单位
- cost_price: 成本价
- sale_price: 销售价
- stock_min: 最小库存
- stock_max: 最大库存
- current_stock: 当前库存
- status: 状态（active/inactive）
- created_at: 创建时间
- updated_at: 更新时间

## 开发计划

### 🚧 第二阶段（计划中）
- 成品管理完整功能
- 库存管理
- 采购管理
- 销售管理

### 📋 第三阶段（计划中）
- 财务管理
- 报表统计
- 系统设置
- 权限管理

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
